import { animate, stagger } from "motion";
import Lenis from "lenis";

document.addEventListener("DOMContentLoaded", function () {
  initializeLenis();
  initializeAnimations();
});

function initializeLenis() {
  const lenis = new Lenis({
    duration: 1.2,
    easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
    smooth: true,
  });

  function raf(time) {
    lenis.raf(time);
    requestAnimationFrame(raf);
  }

  requestAnimationFrame(raf);
}

function initializeAnimations() {
  animate(
    "#headline span span",
    {
      opacity: [0, 1],
      y: [10, 0],
    },
    {
      type: "spring",
      stiffness: 400,
      damping: 40,
      mass: 1,
      delay: stagger(0.05),
    }
  );

  // Button hover animation
  const button = document.querySelector(".wwu-button a");
  const arrowIcon = document.querySelector(".arrow-icon");
  const arrowBg = document.querySelector(".arrow-bg");
  const buttonText = document.querySelector(".wwu-text-container p");

  if (button && arrowIcon && arrowBg && buttonText) {
    button.addEventListener("mouseenter", () => {
      // Animate arrow rotation to 0deg
      animate(
        arrowIcon,
        { rotate: ["-45deg", "0deg"] },
        {
          type: "spring",
          duration: 0.8,
          bounce: 0.2,
        }
      );

      // Animate white circle expansion to scale 10
      animate(
        arrowBg,
        { scale: [1, 10] },
        {
          type: "spring",
          duration: 0.8,
          bounce: 0.2,
        }
      );

      // Animate text color to black
      animate(
        buttonText,
        { color: ["#ffffff", "#000000"] },
        {
          type: "spring",
          duration: 0.8,
          bounce: 0.2,
        }
      );
    });

    button.addEventListener("mouseleave", () => {
      // Animate arrow rotation back to -45deg
      animate(
        arrowIcon,
        { rotate: ["0deg", "-45deg"] },
        {
          type: "spring",
          duration: 0.8,
          bounce: 0.2,
        }
      );

      // Animate white circle back to normal size
      animate(
        arrowBg,
        { scale: [10, 1] },
        {
          type: "spring",
          duration: 0.8,
          bounce: 0.2,
        }
      );

      // Animate text color back to white
      animate(
        buttonText,
        { color: ["#000000", "#ffffff"] },
        {
          type: "spring",
          duration: 0.8,
          bounce: 0.2,
        }
      );
    });
  }
}
