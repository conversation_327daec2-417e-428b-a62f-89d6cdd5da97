import { animate, stagger } from "motion";
import Len<PERSON> from "lenis";

document.addEventListener("DOMContentLoaded", function () {
  initializeLenis();
  initializeAnimations();
});

function initializeLenis() {
  const lenis = new Lenis({
    duration: 1.2,
    easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
    smooth: true,
  });

  function raf(time) {
    lenis.raf(time);
    requestAnimationFrame(raf);
  }

  requestAnimationFrame(raf);
}

function initializeAnimations() {
  animate(
    "#headline span span",
    {
      opacity: [0, 1],
      y: [20, 0],
    },
    {
      duration: 0.8,
      delay: stagger(0.1),
      easing: "ease-out",
    }
  );
}
