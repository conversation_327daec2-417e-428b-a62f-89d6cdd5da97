/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    display: block;
    box-sizing: border-box;
    -webkit-font-smoothing: inherit;
}

:root {
    /* Colors */
    --primary-black: #101014;
    --primary-white: #ffffff;
    --gray-light: #f5f5f5;
    --gray-medium: #666666;
    --gray-dark: #333333;
    --accent-warm: #d4a574;
    --accent-gold: #ffd700;

    /* Typography */
    --font-family: Man<PERSON><PERSON>, "Manrope Placeholder", sans-serif;
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-xxl: 4rem;

    /* <PERSON> Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 16px;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
}

body {
    font-family: var(--font-family);
    font-weight: var(--font-weight-regular);
    line-height: 1.6;
    color: var(--gray-dark);
    background-color: var(--primary-white);
}

/* change */
.first-container {
    display: contents;
}

/* change */
.container-full {
    place-content: center flex-start;
    align-items: center;
    background-color: var(--primary-white);
    display: flex;
    flex-flow: column;
    gap: 0px;
    height: min-content;
    overflow: visible;
    padding: 0px;
    position: relative;
    width: 100%;
}

/* change */
.topbar {
    flex: 0 0 auto;
    height: auto;
    left: 0px;
    position: absolute;
    top: 0px;
    width: 100%;
    z-index: 10;
}

/* change */
.header {
    place-content: flex-start center;
    align-items: flex-start;
    display: flex;
    flex-flow: row;
    gap: 80px;
    height: min-content;
    overflow: hidden;
    padding: 0px;
    position: relative;
    width: 1440px;
}

/* change */
.header-container {
    place-content: center space-between;
    align-items: center;
    display: flex;
    flex: 0 0 auto;
    flex-flow: row;
    height: min-content;
    max-width: 1360px;
    overflow: visible;
    padding: 45px 40px 0px;
    position: relative;
    width: 100%;
}

/* change */
.logo-hamburger {
    place-content: center flex-start;
    align-items: center;
    display: flex;
    flex: 1 0 0px;
    flex-flow: row;
    gap: 10px;
    height: min-content;
    overflow: hidden;
    padding: 0px;
    position: relative;
    width: 1px;
}

/* change */
.logo {
    aspect-ratio: 2.66667 / 1;
    flex: 0 0 auto;
    height: 24px;
    position: relative;
    text-decoration: none;
    width: 64px;
}

/* change */
.nav {
    place-content: center;
    align-items: center;
    display: flex;
    flex: 0 0 auto;
    flex-flow: row;
    gap: 30px;
    height: min-content;
    overflow: hidden;
    padding: 0px;
    position: relative;
    width: min-content;
}

/* change */
.nav-item {
    place-content: center;
    align-items: center;
    display: flex;
    flex: 0 0 auto;
    flex-flow: row;
    gap: 10px;
    height: min-content;
    overflow: hidden;
    padding: 10px;
    position: relative;
    text-decoration: none;
    width: min-content;
}

/* change */
.rich-text-container {
    flex: 0 0 auto;
    height: auto;
    position: relative;
    white-space: pre;
    width: auto;
}

/* change */
.nav-text {
    font-family: var(--font-family);
    font-size: 18px;
    font-weight: var(--font-weight-medium);
    font-style: normal;
    color: var(--primary-white);
    letter-spacing: -0.1px;
    line-height: 100%;
    text-align: left;
    text-decoration: none;
    text-transform: none;
    margin: 0;
    padding: 0;
}

/* change */
.root-container {
    place-content: center flex-start;
    align-items: center;
    background-color: var(--primary-white);
    display: flex;
    flex-flow: column;
    gap: 0px;
    height: min-content;
    overflow: visible;
    padding: 0px;
    position: relative;
    width: 1440px;
}

/* change */
.dummy {
    flex: 0 0 auto;
    height: auto;
    left: 50%;
    position: absolute;
    top: 0px;
    transform: translateX(-50%);
    width: auto;
    z-index: 1;
}

/* change */
.main-content {
    place-content: center;
    align-items: center;
    display: flex;
    flex: 0 0 auto;
    flex-flow: column;
    gap: 150px;
    height: min-content;
    overflow: visible;
    padding: 0px;
    position: relative;
    width: 100%;
}

/* Hero Section */
/* change */
.hero {
    place-content: center;
    align-items: center;
    background-color: var(--primary-black);
    display: flex;
    flex: 0 0 auto;
    flex-flow: column;
    gap: 0px;
    height: 100vh;
    min-height: 700px;
    overflow: hidden;
    padding: 50px 0px 0px;
    position: relative;
    width: 100%;
}

/* change */
.hero-bg {
    place-content: center flex-end;
    align-items: center;
    display: flex;
    flex: 0 0 auto;
    flex-flow: row;
    gap: 0px;
    height: 100%;
    left: 0px;
    overflow: hidden;
    padding: 0px;
    position: absolute;
    top: 0px;
    width: 100%;
}

/* change */
.hero-image-wrapper {
    place-content: center;
    align-items: center;
    display: flex;
    flex: 0.45 0 0px;
    flex-flow: row;
    gap: 10px;
    height: 100%;
    overflow: hidden;
    padding: 30px;
    position: relative;
    width: 1px;
}

/* change */
.hero-image {
    place-content: center;
    align-items: center;
    display: flex;
    flex: 1 0 0px;
    flex-flow: column;
    gap: 10px;
    height: 100%;
    overflow: hidden;
    padding: 0px;
    position: relative;
    width: 1px;
    will-change: transform;
}

/* change */
.hero-image-overlay {
    background: linear-gradient(180deg, var(--primary-black) 0%, rgba(171, 171, 171, 0) 100%);
    flex: 0 0 auto;
    height: 20%;
    left: 0px;
    opacity: 0.8;
    overflow: hidden;
    position: absolute;
    top: 0px;
    width: 100%;
    z-index: 1;
}

/* change */
.hero-image-container {
    border-radius: 10px;
    flex: 0 0 auto;
    height: 100%;
    position: relative;
    width: 100%;
}

/* change */
.hero-image-container img {
    display: block;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    object-position: center center;
    object-fit: cover
}

/* change */
.hero-container {
    place-content: flex-start center;
    align-items: flex-start;
    display: flex;
    flex: 0 0 auto;
    flex-flow: column;
    gap: 10px;
    height: min-content;
    max-width: 1360px;
    overflow: visible;
    padding: 0px 40px;
    position: relative;
    width: 100%;
    z-index: 1;
}


/* change */
.hero-content {
    place-content: flex-start;
    align-items: flex-start;
    display: flex;
    flex: 0 0 auto;
    flex-flow: column;
    gap: 30px;
    height: min-content;
    max-width: 45%;
    overflow: visible;
    padding: 0px;
    position: relative;
    width: 100%;
}

/* change */
.hero-title {
    place-content: flex-start center;
    align-items: flex-start;
    display: flex;
    flex: 0 0 auto;
    flex-flow: column;
    gap: 10px;
    height: min-content;
    overflow: visible;
    padding: 0px;
    position: relative;
    width: 100%;
}

.hero-title-text-container {
    filter: contrast(1) invert(0);
    flex: 0 0 auto;
    height: auto;
    position: relative;
    /* white-space: pre-wrap; */
    width: 100%;
}

.hero-title-text {
    font-family: var(--font-family);
    font-style: normal;
    font-weight: var(--font-weight-medium);
    color: var(--primary-white);
    font-size: 58px;
    letter-spacing: -1px;
    line-height: 120%;
    text-align: start;
    text-decoration: none;
    margin: 0;
    padding: 0;
    word-break: keep-all;
    overflow-wrap: normal;
}

/* Word-level spans - allow wrapping between words but not within words */
#headline>span {
    display: inline-block;
    margin-right: 0.3em;
}

/* Remove margin from the last word span */
#headline>span:last-child {
    margin-right: 0;
}

/* Letter-level spans within each word - animate individually */
#headline span span {
    display: inline-block;
    will-change: transform, opacity;
}


/* change */
.hero-text-container-description {
    place-content: flex-start center;
    align-items: flex-start;
    display: flex;
    flex: 0 0 auto;
    flex-flow: column;
    gap: 40px;
    height: min-content;
    overflow: visible;
    padding: 0px 100px 0px 0px;
    position: relative;
    width: 100%;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-lg);
    font-size: 0.9rem;
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-lg);
}


.hero-description {
    flex: 0 0 auto;
    height: auto;
    max-width: 100%;
    position: relative;
    white-space: normal;
    width: 100%;
    will-change: transform;
    word-break: break-word;
    overflow-wrap: break-word;
}

.hero-description p {
    font-family: var(--font-family);
    font-size: 22px;
    font-weight: var(--font-weight-regular);
    color: #d0d1db;
    letter-spacing: -0.3px;
    line-height: 150%;
    text-align: left;
    margin: 0;
    padding: 0;
}


.cta-button {
    background: var(--primary-white);
    color: var(--primary-black);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-family);
    font-weight: var(--font-weight-semibold);
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.cta-button:hover {
    background: var(--gray-light);
    transform: translateY(-2px);
}





.hero-testimonial {
    position: absolute;
    bottom: 24px;
    right: 24px;
    background: var(--primary-white);
    color: var(--gray-dark);
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    width: 280px;
    z-index: 10;
}

.stars {
    color: var(--accent-gold);
    margin-bottom: 8px;
    font-size: 14px;
}

.hero-testimonial p {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 12px;
    color: var(--gray-dark);
}

.use-free {
    color: var(--primary-black);
    text-decoration: none;
    font-weight: var(--font-weight-semibold);
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}