{"name": "portfolio", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/Aruntd008/portfolio.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/Aruntd008/portfolio/issues"}, "homepage": "https://github.com/Aruntd008/portfolio#readme", "devDependencies": {"motion": "^12.23.0", "vite": "^7.0.3"}, "dependencies": {"lenis": "^1.3.4"}}