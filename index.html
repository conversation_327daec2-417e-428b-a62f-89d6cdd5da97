<!DOCTYPE html>
<html lang="en" style="--svh: 10.94px;">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Manrope Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap"
        rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="src/css/styles.css">
</head>

<body>
    <main>
        <div class="first-container">
            <div class="container-full">
                <div class="topbar">
                    <header class="header"
                        style="width: 100%; transform: none; transform-origin: 50% 50% 0px; opacity: 1;">
                        <div class="header-container"
                            style="transform: none; transform-origin: 50% 50% 0px; opacity: 1;">
                            <div class="logo-hamburger"
                                style="backdrop-filter: none; background-color: rgba(0, 0, 0, 0); border-radius: 0px; will-change: auto; transform: none; transform-origin: 50% 50% 0px; opacity: 1;">
                                <a href="/" class="logo">Your Logo</a>
                            </div>
                            <nav class="nav"
                                style="backdrop-filter: none; background-color: rgba(0, 0, 0, 0); will-change: auto; transform: none; transform-origin: 50% 50% 0px; opacity: 1;">
                                <a href="#about" class="nav-item"
                                    style="transform: none; transform-origin: 50% 50% 0px; opacity: 1;">
                                    <div class="rich-text-container"
                                        style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-link-text-color: rgb(0, 153, 255); --framer-link-text-decoration: underline; transform: none; transform-origin: 50% 50% 0px; opacity: 1;">
                                        <p class="nav-text">About</p>
                                    </div>
                                </a>
                                <a href="#services" class="nav-item"
                                    style="transform: none; transform-origin: 50% 50% 0px; opacity: 1;">
                                    <div class="rich-text-container"
                                        style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-link-text-color: rgb(0, 153, 255); --framer-link-text-decoration: underline; transform: none; transform-origin: 50% 50% 0px; opacity: 1;">
                                        <p class="nav-text">Services</p>
                                    </div>
                                </a>
                                <a href="#our-work" class="nav-item"
                                    style="transform: none; transform-origin: 50% 50% 0px; opacity: 1;">
                                    <div class="rich-text-container"
                                        style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-link-text-color: rgb(0, 153, 255); --framer-link-text-decoration: underline; transform: none; transform-origin: 50% 50% 0px; opacity: 1;">
                                        <p class="nav-text">Our Work</p>
                                    </div>
                                </a>
                                <a href="#faqs" class="nav-item"
                                    style="transform: none; transform-origin: 50% 50% 0px; opacity: 1;">
                                    <div class="rich-text-container"
                                        style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-link-text-color: rgb(0, 153, 255); --framer-link-text-decoration: underline; transform: none; transform-origin: 50% 50% 0px; opacity: 1;">
                                        <p class="nav-text">FAQs</p>
                                    </div>
                                </a>
                                <a href="#contact" class="nav-item"
                                    style="transform: none; transform-origin: 50% 50% 0px; opacity: 1;">
                                    <div class="rich-text-container"
                                        style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; --framer-link-text-color: rgb(0, 153, 255); --framer-link-text-decoration: underline; transform: none; transform-origin: 50% 50% 0px; opacity: 1;">
                                        <p class="nav-text">Contact</p>
                                    </div>
                                </a>
                            </nav>
                        </div>
                    </header>
                </div>
                <div class="root-container" style="min-height: 100%; width: 100%; display: contents;">
                    <div class="dummy">
                        <div></div>
                    </div>
                    <main class="main-content">
                        <section class="hero">
                            <!-- Image Content -->
                            <div class="hero-bg">
                                <div class="hero-image-wrapper">
                                    <div class="hero-image"
                                        style=" opacity: 1; transform: none; will-change: transform;">
                                        <div class="hero-image-overlay"></div>

                                        <div class="hero-image-container">
                                            <div style="position: absolute; border-radius: inherit; inset: 0px;">
                                                <img src="/images/kitchen-hero.avif" alt="Modern kitchen interior">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="hero-container">
                                <div class="hero-content">
                                    <div class="hero-title">
                                        <p class="hero-badge">🏠 Available for work</p>
                                        <div class="hero-title-text-container"
                                            style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; flex-shrink: 0; transform: none;">
                                            <h1 id="headline" class="hero-title-text">
                                                <!-- Each word wrapped in a span with white-space: nowrap, letters individually wrapped -->
                                                <span style="white-space: nowrap;">
                                                    <span>Y</span><span>o</span><span>u</span><span>r</span>
                                                </span>
                                                <span style="white-space: nowrap;">
                                                    <span>t</span><span>r</span><span>u</span><span>s</span><span>t</span><span>e</span><span>d</span>
                                                </span>
                                                <span style="white-space: nowrap;">
                                                    <span>p</span><span>a</span><span>r</span><span>t</span><span>n</span><span>e</span><span>r</span>
                                                </span>
                                                <span style="white-space: nowrap;">
                                                    <span>f</span><span>o</span><span>r</span>
                                                </span>
                                                <span style="white-space: nowrap;">
                                                    <span>q</span><span>u</span><span>a</span><span>l</span><span>i</span><span>t</span><span>y</span>
                                                </span>
                                                <span style="white-space: nowrap;">
                                                    <span>h</span><span>o</span><span>m</span><span>e</span>
                                                </span>
                                                <span style="white-space: nowrap;">
                                                    <span>i</span><span>m</span><span>p</span><span>r</span><span>o</span><span>v</span><span>e</span><span>m</span><span>e</span><span>n</span><span>t</span>
                                                </span>
                                            </h1>

                                        </div>

                                    </div>
                                    <div class="hero-text-container-description">
                                        <div class="hero-description"
                                            style="outline: none; display: flex; flex-direction: column; justify-content: flex-start; opacity: 1; flex-shrink: 0; transform: none; will-change: transform;">
                                            <p>Refit delivers expert home improvements, creating beautiful
                                                and functional spaces with quality craftsmanship.</p>
                                        </div>

                                        <button class="cta-button">Work with us →</button>
                                    </div>

                                </div>


                                <!-- <div class="hero-testimonial">
                                    <div class="stars">⭐⭐⭐⭐⭐</div>
                                    <p>"Refit has been a game changer for my home..."</p>
                                    <a href="#" class="use-free">Use for free →</a>
                                </div> -->
                            </div>
                        </section>
                    </main>
                </div>
            </div>
        </div>
    </main>




    <!-- JavaScript -->
    <script type="module" src="src/js/main.js"></script>
</body>

</html>