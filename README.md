# Refit Portfolio Website

A modern, responsive portfolio website for Refit - a home improvement company. Built with vanilla HTML, CSS, and JavaScript using the Motion library for smooth animations.

## Features

- **Modern Design**: Clean, professional layout based on the provided mockups
- **Responsive**: Mobile-first approach with responsive grid layouts
- **Smooth Animations**: Motion library integration for fluid page transitions and interactions
- **Typography**: Manrope font family for consistent, readable text
- **Interactive Elements**: Accordion services section, smooth scrolling navigation
- **Performance**: Vite build system for fast development and optimized production builds

## Tech Stack

- **HTML5**: Semantic markup structure
- **CSS3**: Modern CSS with custom properties and Grid/Flexbox
- **JavaScript ES6+**: Modern JavaScript with modules
- **Motion**: Animation library for smooth interactions
- **Vite**: Fast build tool and development server

## Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/Aruntd008/portfolio.git
cd portfolio
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:3000`

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally

## Project Structure

```
portfolio/
├── src/
│   ├── css/
│   │   └── styles.css          # Main stylesheet
│   ├── js/
│   │   └── main.js             # Main JavaScript file
│   └── assets/
│       ├── images/             # Image assets
│       └── fonts/              # Font files
├── mockup_images/              # Design mockups
├── index.html                  # Main HTML file
├── vite.config.js             # Vite configuration
├── package.json               # Project dependencies
└── README.md                  # This file
```

## Design System

### Colors
- Primary Black: `#1a1a1a`
- Primary White: `#ffffff`
- Gray Light: `#f5f5f5`
- Gray Medium: `#666666`
- Gray Dark: `#333333`

### Typography
- Font Family: Manrope
- Weights: 300, 400, 500, 600, 700, 800

### Spacing
- XS: 0.5rem
- SM: 1rem
- MD: 1.5rem
- LG: 2rem
- XL: 3rem
- XXL: 4rem

## Animations

The website uses the Motion library for:
- Page load animations
- Scroll-triggered reveals
- Interactive hover effects
- Smooth transitions between states
- Parallax scrolling effects

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the ISC License.